"use client";

import { useState } from "react";
import { MessageSquare, Settings, User, MoreHorizontal, Search, Archive, Trash2 } from "lucide-react";
import ThemeToggle from "./ThemeToggle";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const chatHistory = [
    { id: 1, title: "Hello! How can I help you today?", time: "2m ago", isActive: true },
    { id: 2, title: "Help with React components", time: "1h ago", isActive: false },
    { id: 3, title: "JavaScript best practices", time: "3h ago", isActive: false },
    { id: 4, title: "API integration questions", time: "1d ago", isActive: false },
    { id: 5, title: "CSS Grid vs Flexbox comparison", time: "2d ago", isActive: false },
    { id: 6, title: "Database optimization tips", time: "3d ago", isActive: false },
  ];

  const filteredChats = chatHistory.filter(chat =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        fixed lg:relative inset-y-0 left-0 z-50 lg:z-auto
        w-72 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700
        flex flex-col transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        lg:block
      `}>
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        {/* Search Bar */}
        <div className="relative">
          <Search size={14} className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-8 pr-3 py-1.5 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md text-xs text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Chat History */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-3">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">Recent chats</h3>
            <div className="flex items-center gap-1">
              <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors" title="Archive">
                <Archive size={12} className="text-gray-400" />
              </button>
              <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors" title="Delete">
                <Trash2 size={12} className="text-gray-400" />
              </button>
            </div>
          </div>

          <div className="space-y-0.5">
            {filteredChats.length > 0 ? (
              filteredChats.map((chat) => (
                <div
                  key={chat.id}
                  className={`group flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${
                    chat.isActive
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className={`w-1.5 h-1.5 rounded-full flex-shrink-0 ${
                      chat.isActive ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
                    }`}></div>
                    <div className="min-w-0 flex-1">
                      <div className={`text-xs truncate ${
                        chat.isActive
                          ? 'text-blue-900 dark:text-blue-100 font-medium'
                          : 'text-gray-900 dark:text-white'
                      }`}>
                        {chat.title}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {chat.time}
                      </div>
                    </div>
                  </div>
                  <button className="opacity-0 group-hover:opacity-100 p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-all">
                    <MoreHorizontal size={12} className="text-gray-400" />
                  </button>
                </div>
              ))
            ) : (
              <div className="text-center py-6">
                <MessageSquare size={24} className="text-gray-300 dark:text-gray-600 mx-auto mb-2" />
                <p className="text-xs text-gray-500 dark:text-gray-400">No conversations found</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
              <User size={14} className="text-white" />
            </div>
            <div>
              <div className="text-xs font-medium text-gray-900 dark:text-white">Guest</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Free plan</div>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <ThemeToggle />
            <button className="p-1.5 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
              <Settings size={14} className="text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </aside>
    </>
  );
}
