"use client";

import { useState, useEffect, useRef } from "react";
import { Plus, MoreHorizontal, ChevronDown, Lock, Globe, Menu } from "lucide-react";

interface TopBarProps {
  onToggleSidebar: () => void;
}

export default function TopBar({ onToggleSidebar }: TopBarProps) {
  const [selectedModel, setSelectedModel] = useState("Chat model");
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [isPrivate, setIsPrivate] = useState(false);
  const [isPrivateDropdownOpen, setIsPrivateDropdownOpen] = useState(false);
  const topBarRef = useRef<HTMLDivElement>(null);

  // Function to close all dropdowns
  const closeAllDropdowns = () => {
    setIsModelDropdownOpen(false);
    setIsPrivateDropdownOpen(false);
  };

  // Function to toggle model dropdown
  const toggleModelDropdown = () => {
    if (isPrivateDropdownOpen) setIsPrivateDropdownOpen(false);
    setIsModelDropdownOpen(!isModelDropdownOpen);
  };

  // Function to toggle private dropdown
  const togglePrivateDropdown = () => {
    if (isModelDropdownOpen) setIsModelDropdownOpen(false);
    setIsPrivateDropdownOpen(!isPrivateDropdownOpen);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (topBarRef.current && !topBarRef.current.contains(event.target as Node)) {
        closeAllDropdowns();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const chatModels = [
    { id: "gpt-4", name: "GPT-4", description: "Most capable model" },
    { id: "gpt-3.5", name: "GPT-3.5 Turbo", description: "Fast and efficient" },
    { id: "claude", name: "Claude", description: "Anthropic's AI assistant" },
  ];

  return (
    <div ref={topBarRef} className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between px-3 sm:px-4 py-3">
        {/* Left Section */}
        <div className="flex items-center gap-3">
          {/* Mobile Menu Toggle */}
          <button
            onClick={onToggleSidebar}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors lg:hidden"
            title="Toggle Sidebar"
          >
            <Menu size={18} className="text-gray-600 dark:text-gray-400" />
          </button>

          {/* Title */}
          <h1 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
            Chatbot
          </h1>

          {/* New Chat Button */}
          <button
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="New Chat"
          >
            <Plus size={18} className="text-gray-600 dark:text-gray-400" />
          </button>

          {/* Menu Button */}
          <button
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Menu"
          >
            <MoreHorizontal size={18} className="text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Center Section */}
        <div className="hidden sm:flex items-center gap-4">
          {/* Chat Model Selector */}
          <div className="relative">
            <button
              onClick={toggleModelDropdown}
              className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors border border-gray-200 dark:border-gray-600"
            >
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {selectedModel}
              </span>
              <ChevronDown size={14} className="text-gray-500 dark:text-gray-400" />
            </button>

            {isModelDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-56 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 dropdown-enter">
                <div className="p-1">
                  {chatModels.map((model) => (
                    <button
                      key={model.id}
                      onClick={() => {
                        setSelectedModel(model.name);
                        closeAllDropdowns();
                      }}
                      className="w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      <div className="text-xs font-medium text-gray-900 dark:text-white">
                        {model.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {model.description}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Private Toggle */}
          <div className="relative">
            <button
              onClick={togglePrivateDropdown}
              className="flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors border border-gray-200 dark:border-gray-600"
            >
              {isPrivate ? (
                <Lock size={14} className="text-gray-600 dark:text-gray-400" />
              ) : (
                <Globe size={14} className="text-gray-600 dark:text-gray-400" />
              )}
              <span className="text-xs font-medium text-gray-900 dark:text-white">
                {isPrivate ? "Private" : "Public"}
              </span>
              <ChevronDown size={14} className="text-gray-500 dark:text-gray-400" />
            </button>

            {isPrivateDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 dropdown-enter">
                <div className="p-1">
                  <button
                    onClick={() => {
                      setIsPrivate(false);
                      closeAllDropdowns();
                    }}
                    className="w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-2"
                  >
                    <Globe size={14} className="text-gray-600 dark:text-gray-400" />
                    <div>
                      <div className="text-xs font-medium text-gray-900 dark:text-white">
                        Public
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Anyone can view this chat
                      </div>
                    </div>
                  </button>
                  <button
                    onClick={() => {
                      setIsPrivate(true);
                      closeAllDropdowns();
                    }}
                    className="w-full text-left p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex items-center gap-2"
                  >
                    <Lock size={14} className="text-gray-600 dark:text-gray-400" />
                    <div>
                      <div className="text-xs font-medium text-gray-900 dark:text-white">
                        Private
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Only you can view this chat
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-2">
          {/* Today indicator */}
          <span className="text-sm text-gray-500 dark:text-gray-400">Today</span>
        </div>
      </div>
    </div>
  );
}
